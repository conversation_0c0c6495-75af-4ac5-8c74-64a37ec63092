/**
 * 资产加载器类
 * 负责加载各种类型的资产
 */
import * as THREE from 'three';
import { GLTFLoader } from 'three/addons/loaders/GLTFLoader.js';
import { FBXLoader } from 'three/addons/loaders/FBXLoader.js';
import { OBJLoader } from 'three/addons/loaders/OBJLoader.js';
import { FontLoader } from 'three/addons/loaders/FontLoader.js';
import { AssetType } from './ResourceManager';

export class AssetLoader {
  /** Three.js加载管理器 */
  private manager: THREE.LoadingManager;

  /** 纹理加载器 */
  private textureLoader: THREE.TextureLoader;

  /** GLTF加载器 */
  private gltfLoader: GLTFLoader;

  /** FBX加载器 */
  private fbxLoader: FBXLoader;

  /** OBJ加载器 */
  private objLoader: OBJLoader;

  /** 立方体纹理加载器 */
  private cubeTextureLoader: THREE.CubeTextureLoader;

  /** 音频加载器 */
  private audioLoader: THREE.AudioLoader;

  /** 字体加载器 */
  private fontLoader: FontLoader;

  /** 文件加载器 */
  private fileLoader: THREE.FileLoader;

  /**
   * 创建资产加载器实例
   */
  constructor() {
    // 创建加载管理器
    this.manager = new THREE.LoadingManager();

    // 创建各种加载器
    this.textureLoader = new THREE.TextureLoader(this.manager);
    this.gltfLoader = new GLTFLoader(this.manager);
    this.fbxLoader = new FBXLoader(this.manager);
    this.objLoader = new OBJLoader(this.manager);
    this.cubeTextureLoader = new THREE.CubeTextureLoader(this.manager);
    this.audioLoader = new THREE.AudioLoader(this.manager);
    this.fontLoader = new FontLoader(this.manager);
    this.fileLoader = new THREE.FileLoader(this.manager);
  }

  /**
   * 加载资产
   * @param type 资产类型
   * @param url 资产URL
   * @returns Promise，解析为加载的资产数据
   */
  public async load(type: AssetType, url: string): Promise<any> {
    switch (type) {
      case AssetType.TEXTURE:
        return this.loadTexture(url);

      case AssetType.MODEL:
        return this.loadModel(url);

      case AssetType.AUDIO:
        return this.loadAudio(url);

      case AssetType.FONT:
        return this.loadFont(url);

      case AssetType.JSON:
        return this.loadJSON(url);

      case AssetType.TEXT:
        return this.loadText(url);

      case AssetType.BINARY:
        return this.loadBinary(url);

      case AssetType.CUBEMAP:
        return this.loadCubeTexture(url);

      default:
        throw new Error(`不支持的资产类型: ${type}`);
    }
  }

  /**
   * 加载纹理
   * @param url 纹理URL
   * @returns Promise，解析为加载的纹理
   */
  private loadTexture(url: string): Promise<THREE.Texture> {
    return new Promise((resolve, reject) => {
      this.textureLoader.load(
        url,
        texture => {
          // 设置纹理属性
          texture.colorSpace = THREE.SRGBColorSpace;
          texture.needsUpdate = true;

          resolve(texture);
        },
        undefined,
        error => reject(new Error(`加载纹理失败: ${error.message}`))
      );
    });
  }

  /**
   * 加载模型
   * @param url 模型URL
   * @returns Promise，解析为加载的模型
   */
  private loadModel(url: string): Promise<any> {
    // 根据文件扩展名选择加载器
    const extension = url.split('.').pop()?.toLowerCase();

    switch (extension) {
      case 'gltf':
      case 'glb':
        return this.loadGLTF(url);

      case 'fbx':
        return this.loadFBX(url);

      case 'obj':
        return this.loadOBJ(url);

      default:
        throw new Error(`不支持的模型格式: ${extension}`);
    }
  }

  /**
   * 加载GLTF模型
   * @param url GLTF模型URL
   * @returns Promise，解析为加载的GLTF模型
   */
  private loadGLTF(url: string): Promise<any> {
    return new Promise((resolve, reject) => {
      this.gltfLoader.load(
        url,
        (gltf: any) => resolve(gltf),
        undefined,
        (error: any) => reject(new Error(`加载GLTF模型失败: ${error.message}`))
      );
    });
  }

  /**
   * 加载FBX模型
   * @param url FBX模型URL
   * @returns Promise，解析为加载的FBX模型
   */
  private loadFBX(url: string): Promise<any> {
    return new Promise((resolve, reject) => {
      this.fbxLoader.load(
        url,
        (fbx: any) => resolve(fbx),
        undefined,
        (error: any) => reject(new Error(`加载FBX模型失败: ${error.message}`))
      );
    });
  }

  /**
   * 加载OBJ模型
   * @param url OBJ模型URL
   * @returns Promise，解析为加载的OBJ模型
   */
  private loadOBJ(url: string): Promise<any> {
    return new Promise((resolve, reject) => {
      this.objLoader.load(
        url,
        (obj: any) => resolve(obj),
        undefined,
        (error: any) => reject(new Error(`加载OBJ模型失败: ${error.message}`))
      );
    });
  }

  /**
   * 加载音频
   * @param url 音频URL
   * @returns Promise，解析为加载的音频数据
   */
  private loadAudio(url: string): Promise<AudioBuffer> {
    return new Promise((resolve, reject) => {
      this.audioLoader.load(
        url,
        (buffer: AudioBuffer) => resolve(buffer),
        undefined,
        (error: any) => reject(new Error(`加载音频失败: ${error.message}`))
      );
    });
  }

  /**
   * 加载字体
   * @param url 字体URL
   * @returns Promise，解析为加载的字体
   */
  private loadFont(url: string): Promise<any> {
    return new Promise((resolve, reject) => {
      this.fontLoader.load(
        url,
        (font: any) => resolve(font),
        undefined,
        (error: any) => reject(new Error(`加载字体失败: ${error.message}`))
      );
    });
  }

  /**
   * 加载JSON
   * @param url JSON URL
   * @returns Promise，解析为加载的JSON数据
   */
  private loadJSON(url: string): Promise<any> {
    return new Promise((resolve, reject) => {
      this.fileLoader.setResponseType('json');
      this.fileLoader.load(
        url,
        data => resolve(data),
        undefined,
        error => reject(new Error(`加载JSON失败: ${error.message}`))
      );
    });
  }

  /**
   * 加载文本
   * @param url 文本URL
   * @returns Promise，解析为加载的文本
   */
  private loadText(url: string): Promise<string> {
    return new Promise((resolve, reject) => {
      this.fileLoader.setResponseType('text');
      this.fileLoader.load(
        url,
        data => resolve(data as string),
        undefined,
        error => reject(new Error(`加载文本失败: ${error.message}`))
      );
    });
  }

  /**
   * 加载二进制数据
   * @param url 二进制数据URL
   * @returns Promise，解析为加载的二进制数据
   */
  private loadBinary(url: string): Promise<ArrayBuffer> {
    return new Promise((resolve, reject) => {
      this.fileLoader.setResponseType('arraybuffer');
      this.fileLoader.load(
        url,
        data => resolve(data as ArrayBuffer),
        undefined,
        error => reject(new Error(`加载二进制数据失败: ${error.message}`))
      );
    });
  }

  /**
   * 加载立方体纹理
   * @param urls 立方体纹理URL数组（顺序：右、左、上、下、前、后）
   * @returns Promise，解析为加载的立方体纹理
   */
  private loadCubeTexture(urls: string | string[]): Promise<THREE.CubeTexture> {
    // 如果是单个URL，解析为数组
    const urlArray = typeof urls === 'string' ? JSON.parse(urls) as string[] : urls;

    if (!Array.isArray(urlArray) || urlArray.length !== 6) {
      throw new Error('立方体纹理需要6个面的贴图路径');
    }

    return new Promise((resolve, reject) => {
      this.cubeTextureLoader.load(
        urlArray,
        texture => {
          // 设置纹理属性
          texture.colorSpace = THREE.SRGBColorSpace;

          resolve(texture);
        },
        undefined,
        error => reject(new Error(`加载立方体纹理失败: ${error.message}`))
      );
    });
  }

  /**
   * 获取加载管理器
   * @returns 加载管理器
   */
  public getManager(): THREE.LoadingManager {
    return this.manager;
  }

  /**
   * 设置加载基础路径
   * @param path 基础路径
   */
  public setPath(path: string): void {
    this.textureLoader.setPath(path);
    this.gltfLoader.setPath(path);
    this.fbxLoader.setPath(path);
    this.objLoader.setPath(path);
    this.cubeTextureLoader.setPath(path);
    this.audioLoader.setPath(path);
    this.fontLoader.setPath(path);
    this.fileLoader.setPath(path);
  }

  /**
   * 设置跨域
   * @param crossOrigin 跨域设置
   */
  public setCrossOrigin(crossOrigin: string): void {
    this.textureLoader.setCrossOrigin(crossOrigin);
    this.cubeTextureLoader.setCrossOrigin(crossOrigin);
    this.fileLoader.setCrossOrigin(crossOrigin);
  }

  /**
   * 销毁加载器
   */
  public dispose(): void {
    // 清除加载管理器的事件监听器
    this.manager.onStart = () => {};
    this.manager.onLoad = () => {};
    this.manager.onProgress = () => {};
    this.manager.onError = () => {};
  }
}
