/**
 * 时间工具类
 * 提供时间相关的功能
 */
export class Time {
  /** 游戏开始时间（毫秒） */
  private static startTime: number = 0;

  /** 当前时间（秒） */
  private static currentTime: number = 0;

  /** 帧间隔时间（秒） */
  private static deltaTime: number = 0;

  /** 固定帧间隔时间（秒） */
  private static fixedDeltaTime: number = 1 / 60;

  /** 时间缩放 */
  private static timeScale: number = 1;

  /** 帧数 */
  private static frameCount: number = 0;

  /** 每秒帧数 */
  private static fps: number = 0;

  /** FPS更新间隔（秒） */
  private static fpsUpdateInterval: number = 0.5;

  /** FPS更新计时器 */
  private static fpsUpdateTimer: number = 0;

  /** FPS计数器 */
  private static fpsCounter: number = 0;

  /**
   * 初始化时间系统
   */
  public static initialize(): void {
    this.startTime = performance.now();
    this.currentTime = 0;
    this.deltaTime = 0;
    this.frameCount = 0;
    this.fps = 0;
    this.fpsUpdateTimer = 0;
    this.fpsCounter = 0;
  }

  /**
   * 更新时间系统
   * @param deltaTime 帧间隔时间（秒）
   */
  public static update(deltaTime: number): void {
    // 更新时间
    this.currentTime += deltaTime;
    this.deltaTime = deltaTime * this.timeScale;

    // 更新帧数
    this.frameCount++;
    this.fpsCounter++;

    // 更新FPS
    this.fpsUpdateTimer += deltaTime;
    if (this.fpsUpdateTimer >= this.fpsUpdateInterval) {
      this.fps = Math.round(this.fpsCounter / this.fpsUpdateTimer);
      this.fpsCounter = 0;
      this.fpsUpdateTimer = 0;
    }
  }

  /**
   * 获取游戏运行时间（秒）
   * @returns 游戏运行时间
   */
  public static getTime(): number {
    return this.currentTime;
  }

  /**
   * 获取游戏开始以来的真实时间（秒）
   * @returns 真实时间
   */
  public static getRealTime(): number {
    return (performance.now() - this.startTime) / 1000;
  }

  /**
   * 获取帧间隔时间（秒）
   * @returns 帧间隔时间
   */
  public static getDeltaTime(): number {
    return this.deltaTime;
  }

  /**
   * 获取未缩放的帧间隔时间（秒）
   * @returns 未缩放的帧间隔时间
   */
  public static getUnscaledDeltaTime(): number {
    return this.deltaTime / this.timeScale;
  }

  /**
   * 获取固定帧间隔时间（秒）
   * @returns 固定帧间隔时间
   */
  public static getFixedDeltaTime(): number {
    return this.fixedDeltaTime;
  }

  /**
   * 设置固定帧间隔时间（秒）
   * @param fixedDeltaTime 固定帧间隔时间
   */
  public static setFixedDeltaTime(fixedDeltaTime: number): void {
    this.fixedDeltaTime = fixedDeltaTime;
  }

  /**
   * 获取时间缩放
   * @returns 时间缩放
   */
  public static getTimeScale(): number {
    return this.timeScale;
  }

  /**
   * 设置时间缩放
   * @param timeScale 时间缩放
   */
  public static setTimeScale(timeScale: number): void {
    this.timeScale = Math.max(0, timeScale);
  }

  /**
   * 获取帧数
   * @returns 帧数
   */
  public static getFrameCount(): number {
    return this.frameCount;
  }

  /**
   * 获取每秒帧数
   * @returns 每秒帧数
   */
  public static getFPS(): number {
    return this.fps;
  }

  /**
   * 获取当前时间戳（毫秒）
   * @returns 当前时间戳
   */
  public static now(): number {
    return performance.now();
  }
}
