/**
 * 后处理系统
 * 用于管理和应用后处理效果
 */
import * as THREE from 'three';
// 使用类型断言导入后处理模块
// @ts-ignore
import { EffectComposer } from 'three/examples/jsm/postprocessing/EffectComposer.js';
// @ts-ignore
import { RenderPass } from 'three/examples/jsm/postprocessing/RenderPass.js';
import { System } from '../../core/System';
import { PostProcessingEffect } from './PostProcessingEffect';
import { EventEmitter } from '../../utils/EventEmitter';

/**
 * 后处理系统选项
 */
export interface PostProcessingSystemOptions {
  /** 是否启用 */
  enabled?: boolean;
  /** 是否自动调整大小 */
  autoResize?: boolean;
  /** 渲染目标选项 */
  renderTargetOptions?: {
    /** 宽度 */
    width?: number;
    /** 高度 */
    height?: number;
    /** 采样级别 */
    samples?: number;
    /** 是否使用深度纹理 */
    depthTexture?: boolean;
    /** 是否使用浮点纹理 */
    floatTexture?: boolean;
  };
}

/**
 * 后处理系统事件类型
 */
export enum PostProcessingEventType {
  /** 效果添加 */
  EFFECT_ADDED = 'effectAdded',
  /** 效果移除 */
  EFFECT_REMOVED = 'effectRemoved',
  /** 效果启用 */
  EFFECT_ENABLED = 'effectEnabled',
  /** 效果禁用 */
  EFFECT_DISABLED = 'effectDisabled',
  /** 效果顺序变更 */
  EFFECT_ORDER_CHANGED = 'effectOrderChanged',
  /** 系统启用 */
  SYSTEM_ENABLED = 'systemEnabled',
  /** 系统禁用 */
  SYSTEM_DISABLED = 'systemDisabled',
  /** 渲染目标调整大小 */
  RESIZE = 'resize'
}

/**
 * 后处理系统
 */
export class PostProcessingSystem extends System {
  /** 系统名称 */
  public static readonly NAME: string = 'PostProcessingSystem';

  /** 系统名称（实例属性） */
  protected name: string = PostProcessingSystem.NAME;

  /** 是否启用后处理 */
  private postProcessingEnabled: boolean = true;

  /** 是否自动调整大小 */
  private autoResize: boolean;

  /** 渲染器 */
  private renderer: THREE.WebGLRenderer | null = null;

  /** 效果合成器 */
  private composer: EffectComposer | null = null;

  /** 渲染通道 */
  private renderPass: RenderPass | null = null;

  /** 效果列表 */
  private effects: PostProcessingEffect[] = [];

  /** 事件发射器 */
  private eventEmitter: EventEmitter = new EventEmitter();

  /** 是否已初始化 */
  private initialized: boolean = false;

  /** 是否已销毁 */
  private destroyed: boolean = false;

  /** 渲染目标宽度 */
  private width: number = 1;

  /** 渲染目标高度 */
  private height: number = 1;

  /** 渲染目标采样级别 */
  private samples: number = 0;

  /** 是否使用深度纹理 */
  private useDepthTexture: boolean = false;

  /** 是否使用浮点纹理 */
  private useFloatTexture: boolean = false;

  /** 渲染目标 */
  private renderTarget: THREE.WebGLRenderTarget | null = null;

  /**
   * 创建后处理系统
   * @param options 后处理系统选项
   */
  constructor(options: PostProcessingSystemOptions = {}) {
    // 使用数字ID代替字符串名称
    super(0);

    // 设置系统名称（仅用于日志和调试）
    this.name = PostProcessingSystem.NAME;

    // 设置是否启用后处理
    this.postProcessingEnabled = options.enabled !== undefined ? options.enabled : true;
    // 调用基类的 setEnabled 方法设置系统是否启用
    super.setEnabled(this.postProcessingEnabled);

    this.autoResize = options.autoResize !== undefined ? options.autoResize : true;

    // 设置渲染目标选项
    const renderTargetOptions = options.renderTargetOptions || {};
    this.width = renderTargetOptions.width || 1;
    this.height = renderTargetOptions.height || 1;
    this.samples = renderTargetOptions.samples || 0;
    this.useDepthTexture = renderTargetOptions.depthTexture || false;
    this.useFloatTexture = renderTargetOptions.floatTexture || false;
  }

  /**
   * 初始化系统
   */
  public initialize(): void {
    if (this.initialized || this.destroyed) return;

    // 基类初始化
    super.initialize();
  }

  /**
   * 检查是否已初始化
   * @returns 是否已初始化
   */
  public isInitialized(): boolean {
    return this.initialized;
  }

  /**
   * 设置渲染器、场景和相机
   * @param renderer 渲染器
   * @param scene 场景
   * @param camera 相机
   */
  public setup(renderer: THREE.WebGLRenderer, scene: THREE.Scene, camera: THREE.Camera): void {
    if (this.initialized || this.destroyed) return;

    this.renderer = renderer;

    // 获取渲染器尺寸
    const size = new THREE.Vector2();
    renderer.getSize(size);
    this.width = size.width;
    this.height = size.height;

    // 创建渲染目标
    this.createRenderTarget();

    // 创建效果合成器
    this.composer = new EffectComposer(renderer, this.renderTarget!);

    // 创建渲染通道
    this.renderPass = new RenderPass(scene, camera);
    this.composer.addPass(this.renderPass);

    // 添加已有效果
    for (const effect of this.effects) {
      this.addEffectPass(effect);
    }

    // 如果启用自动调整大小，则添加窗口大小变化事件监听器
    if (this.autoResize) {
      window.addEventListener('resize', this.handleResize.bind(this));
    }

    this.initialized = true;
  }

  /**
   * 创建渲染目标
   */
  private createRenderTarget(): void {
    // 创建渲染目标参数
    const parameters: THREE.WebGLRenderTargetOptions = {
      minFilter: THREE.LinearFilter,
      magFilter: THREE.LinearFilter,
      format: THREE.RGBAFormat,
      // 使用 colorSpace 替代已弃用的 encoding
      colorSpace: THREE.SRGBColorSpace
    };

    // 如果使用浮点纹理，则设置类型
    if (this.useFloatTexture) {
      parameters.type = THREE.FloatType;
    }

    // 创建渲染目标
    this.renderTarget = new THREE.WebGLRenderTarget(
      this.width,
      this.height,
      parameters
    );

    // 如果支持多重采样，设置采样级别
    if (this.samples > 0 && this.renderer?.capabilities.isWebGL2) {
      // 注意：WebGLMultisampleRenderTarget 已被弃用，但我们可以设置 samples 属性
      (this.renderTarget as any).samples = this.samples;
    }

    // 如果使用深度纹理，则创建深度纹理
    if (this.useDepthTexture && this.renderTarget) {
      // 创建深度纹理
      const depthTexture = new THREE.DepthTexture(
        this.width,
        this.height
      );
      depthTexture.format = THREE.DepthFormat;
      depthTexture.type = THREE.UnsignedShortType;

      // 设置渲染目标的深度纹理
      this.renderTarget.depthTexture = depthTexture;
    }
  }

  /**
   * 处理窗口大小变化
   */
  private handleResize(): void {
    if (!this.initialized || !this.renderer || !this.composer) return;

    // 获取渲染器尺寸
    const size = new THREE.Vector2();
    this.renderer.getSize(size);

    // 调整大小
    this.resize(size.width, size.height);
  }

  /**
   * 调整大小
   * @param width 宽度
   * @param height 高度
   */
  public resize(width: number, height: number): void {
    if (!this.initialized || !this.renderer || !this.composer) return;

    this.width = width;
    this.height = height;

    // 调整渲染目标大小
    this.renderTarget?.setSize(width, height);

    // 调整效果合成器大小
    this.composer.setSize(width, height);

    // 调整效果大小
    for (const effect of this.effects) {
      effect.resize(width, height);
    }

    // 触发调整大小事件
    this.eventEmitter.emit(PostProcessingEventType.RESIZE, { width, height });
  }

  /**
   * 更新系统
   * @param deltaTime 帧间隔时间（秒）
   */
  public update(deltaTime: number): void {
    // 使用 super.isEnabled() 检查系统是否启用
    if (!this.initialized || !super.isEnabled() || !this.postProcessingEnabled || !this.composer || this.destroyed) return;

    // 更新效果
    for (const effect of this.effects) {
      if (effect.isEnabled()) {
        effect.update(deltaTime);
      }
    }

    // 渲染效果合成器
    this.composer.render(deltaTime);
  }

  /**
   * 添加效果
   * @param effect 后处理效果
   * @returns 是否成功添加
   */
  public addEffect(effect: PostProcessingEffect): boolean {
    // 如果已经存在，则返回false
    if (this.effects.includes(effect)) return false;

    // 添加效果
    this.effects.push(effect);

    // 如果已初始化，则添加效果通道
    if (this.initialized && this.composer) {
      this.addEffectPass(effect);
    }

    // 触发效果添加事件
    this.eventEmitter.emit(PostProcessingEventType.EFFECT_ADDED, { effect });

    return true;
  }

  /**
   * 添加效果通道
   * @param effect 后处理效果
   */
  private addEffectPass(effect: PostProcessingEffect): void {
    if (!this.composer || !this.renderer) return;

    // 初始化效果
    effect.initialize(this.renderer, this.width, this.height);

    // 获取效果通道
    const pass = effect.getPass();
    if (pass) {
      // 添加通道
      this.composer.addPass(pass);
    }
  }

  /**
   * 移除效果
   * @param effect 后处理效果
   * @returns 是否成功移除
   */
  public removeEffect(effect: PostProcessingEffect): boolean {
    // 查找效果索引
    const index = this.effects.indexOf(effect);
    if (index === -1) return false;

    // 如果已初始化，则移除效果通道
    if (this.initialized && this.composer) {
      this.removeEffectPass(effect);
    }

    // 移除效果
    this.effects.splice(index, 1);

    // 触发效果移除事件
    this.eventEmitter.emit(PostProcessingEventType.EFFECT_REMOVED, { effect });

    return true;
  }

  /**
   * 移除效果通道
   * @param effect 后处理效果
   */
  private removeEffectPass(effect: PostProcessingEffect): void {
    if (!this.composer) return;

    // 获取效果通道
    const pass = effect.getPass();
    if (pass) {
      // 移除通道
      const passes = this.composer.passes;
      const passIndex = passes.indexOf(pass);
      if (passIndex !== -1) {
        passes.splice(passIndex, 1);
      }
    }

    // 销毁效果
    (effect as any).dispose();
  }

  /**
   * 获取效果
   * @param name 效果名称
   * @returns 后处理效果
   */
  public getEffect(name: string): PostProcessingEffect | null {
    return this.effects.find(effect => effect.getName() === name) || null;
  }

  /**
   * 获取所有效果
   * @returns 后处理效果列表
   */
  public getEffects(): PostProcessingEffect[] {
    return [...this.effects];
  }

  /**
   * 启用效果
   * @param name 效果名称
   * @returns 是否成功启用
   */
  public enableEffect(name: string): boolean {
    const effect = this.getEffect(name);
    if (!effect) return false;

    effect.setEnabled(true);

    // 触发效果启用事件
    this.eventEmitter.emit(PostProcessingEventType.EFFECT_ENABLED, { effect });

    return true;
  }

  /**
   * 禁用效果
   * @param name 效果名称
   * @returns 是否成功禁用
   */
  public disableEffect(name: string): boolean {
    const effect = this.getEffect(name);
    if (!effect) return false;

    effect.setEnabled(false);

    // 触发效果禁用事件
    this.eventEmitter.emit(PostProcessingEventType.EFFECT_DISABLED, { effect });

    return true;
  }

  /**
   * 设置效果顺序
   * @param names 效果名称列表
   * @returns 是否成功设置
   */
  public setEffectOrder(names: string[]): boolean {
    if (!this.initialized || !this.composer) return false;

    // 检查所有名称是否存在
    for (const name of names) {
      if (!this.getEffect(name)) return false;
    }

    // 重新排序效果
    const newEffects: PostProcessingEffect[] = [];
    for (const name of names) {
      const effect = this.getEffect(name);
      if (effect) {
        newEffects.push(effect);
      }
    }

    // 添加未包含在名称列表中的效果
    for (const effect of this.effects) {
      if (!names.includes(effect.getName())) {
        newEffects.push(effect);
      }
    }

    // 更新效果列表
    this.effects = newEffects;

    // 重新创建通道
    this.rebuildPasses();

    // 触发效果顺序变更事件
    this.eventEmitter.emit(PostProcessingEventType.EFFECT_ORDER_CHANGED, { effects: this.effects });

    return true;
  }

  /**
   * 重新构建通道
   */
  private rebuildPasses(): void {
    if (!this.initialized || !this.composer || !this.renderPass) return;

    // 清除通道
    this.composer.passes = [this.renderPass];

    // 添加效果通道
    for (const effect of this.effects) {
      const pass = effect.getPass();
      if (pass) {
        this.composer.addPass(pass);
      }
    }
  }

  /**
   * 设置是否启用
   * @param enabled 是否启用
   */
  public override setEnabled(enabled: boolean): this {
    // 检查状态是否变化
    if (super.isEnabled() === enabled) return this;

    // 设置后处理启用状态
    this.postProcessingEnabled = enabled;

    // 调用基类方法设置系统启用状态
    super.setEnabled(enabled);

    // 触发系统启用/禁用事件
    if (enabled) {
      this.eventEmitter.emit(PostProcessingEventType.SYSTEM_ENABLED);
    } else {
      this.eventEmitter.emit(PostProcessingEventType.SYSTEM_DISABLED);
    }

    return this;
  }

  /**
   * 获取是否启用
   * @returns 是否启用
   */
  public override isEnabled(): boolean {
    // 同时检查系统启用状态和后处理启用状态
    return super.isEnabled() && this.postProcessingEnabled;
  }

  /**
   * 获取渲染目标
   * @returns 渲染目标
   */
  public getRenderTarget(): THREE.WebGLRenderTarget | null {
    return this.renderTarget;
  }

  /**
   * 获取效果合成器
   * @returns 效果合成器
   */
  public getComposer(): EffectComposer | null {
    return this.composer;
  }

  /**
   * 添加事件监听器
   * @param event 事件类型
   * @param callback 监听器函数
   * @returns this 实例
   */
  public override on(event: string, callback: (...args: any[]) => void): this {
    // 使用自定义事件发射器
    this.eventEmitter.on(event, callback);
    return this;
  }

  /**
   * 移除事件监听器
   * @param event 事件类型
   * @param callback 监听器函数
   * @returns this 实例
   */
  public override off(event: string, callback?: (...args: any[]) => void): this {
    // 使用自定义事件发射器
    this.eventEmitter.off(event, callback);
    return this;
  }

  /**
   * 销毁系统
   */
  public dispose(): void {
    if (this.destroyed) return;

    // 移除窗口大小变化事件监听器
    if (this.autoResize) {
      window.removeEventListener('resize', this.handleResize.bind(this));
    }

    // 销毁效果
    for (const effect of this.effects) {
      (effect as any).dispose();
    }

    // 清空效果列表
    this.effects = [];

    // 销毁渲染目标
    this.renderTarget?.dispose();

    // 清空引用
    this.renderTarget = null;
    this.composer = null;
    this.renderPass = null;
    this.renderer = null;

    // 移除所有事件监听器
    this.eventEmitter.removeAllListeners();

    this.initialized = false;
    this.destroyed = true;
  }
}
