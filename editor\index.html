<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>DL引擎编辑器</title>
    <meta name="description" content="DL（Digital Learning）引擎编辑器 - 强大的多媒体游戏引擎开发工具" />
    <meta name="keywords" content="游戏引擎,编辑器,3D,多媒体,开发工具" />
    <style>
      /* 加载动画样式 */
      .loading-container {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        display: flex;
        justify-content: center;
        align-items: center;
        z-index: 9999;
        transition: opacity 0.5s ease-out;
      }
      
      .loading-spinner {
        width: 50px;
        height: 50px;
        border: 3px solid rgba(255, 255, 255, 0.3);
        border-radius: 50%;
        border-top-color: #fff;
        animation: spin 1s ease-in-out infinite;
      }
      
      .loading-text {
        color: white;
        font-size: 18px;
        margin-top: 20px;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
      }
      
      @keyframes spin {
        to { transform: rotate(360deg); }
      }
      
      /* 隐藏加载动画 */
      .loading-container.hidden {
        opacity: 0;
        pointer-events: none;
      }
      
      /* 基础样式重置 */
      * {
        box-sizing: border-box;
      }
      
      body {
        margin: 0;
        padding: 0;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
        background-color: #f0f2f5;
      }
      
      #root {
        width: 100%;
        height: 100vh;
        overflow: hidden;
      }
    </style>
  </head>
  <body>
    <!-- 加载动画 -->
    <div id="loading" class="loading-container">
      <div style="text-align: center;">
        <div class="loading-spinner"></div>
        <div class="loading-text">正在加载 DL引擎编辑器...</div>
      </div>
    </div>
    
    <!-- React 应用挂载点 -->
    <div id="root"></div>
    
    <script type="module" src="/src/main.tsx"></script>
    
    <script>
      // 隐藏加载动画
      window.addEventListener('load', function() {
        setTimeout(function() {
          const loading = document.getElementById('loading');
          if (loading) {
            loading.classList.add('hidden');
            setTimeout(function() {
              loading.style.display = 'none';
            }, 500);
          }
        }, 1000);
      });
      
      // 错误处理
      window.addEventListener('error', function(e) {
        console.error('应用加载错误:', e.error);
      });
      
      // 未处理的 Promise 拒绝
      window.addEventListener('unhandledrejection', function(e) {
        console.error('未处理的 Promise 拒绝:', e.reason);
      });
    </script>
  </body>
</html>
