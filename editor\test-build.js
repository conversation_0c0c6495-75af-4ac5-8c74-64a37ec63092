#!/usr/bin/env node

const { spawn } = require('child_process');
const path = require('path');

console.log('开始测试构建...');

// 首先运行 TypeScript 编译检查
console.log('1. 检查 TypeScript 编译...');
const tsc = spawn('npx', ['tsc', '--noEmit'], {
  cwd: __dirname,
  stdio: 'inherit',
  shell: true
});

tsc.on('close', (code) => {
  if (code !== 0) {
    console.error(`TypeScript 编译检查失败，退出码: ${code}`);
    process.exit(1);
  }
  
  console.log('TypeScript 编译检查通过');
  
  // 然后运行 Vite 构建
  console.log('2. 运行 Vite 构建...');
  const vite = spawn('npx', ['vite', 'build'], {
    cwd: __dirname,
    stdio: 'inherit',
    shell: true
  });
  
  vite.on('close', (code) => {
    if (code !== 0) {
      console.error(`Vite 构建失败，退出码: ${code}`);
      process.exit(1);
    }
    
    console.log('构建成功！');
  });
});
