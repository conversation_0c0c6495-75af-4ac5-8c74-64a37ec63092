/**
 * 面部动画预设服务
 * 连接前端组件和引擎的面部动画预设系统
 */
import EngineService from './EngineService';
import { FacialAnimationPresetType, FacialExpressionType } from '../components/FacialAnimationEditor/FacialAnimationPresetManager';

/**
 * 面部动画预设接口（前端使用）
 */
export interface FacialAnimationPreset {
  id: string;
  name: string;
  type: FacialAnimationPresetType;
  description?: string;
  tags?: string[];
  culture?: string;
  expression?: FacialExpressionType;
  weight?: number;
  expressionCombos?: { expression: FacialExpressionType, weight: number }[];
  animationSequence?: { expression: FacialExpressionType, weight: number, duration: number }[];
  author?: string;
  createdAt?: Date;
  updatedAt?: Date;
  thumbnail?: string;
  isFavorite?: boolean;
}

/**
 * 面部动画预设服务类
 */
export class FacialAnimationPresetService {
  private static instance: FacialAnimationPresetService;

  private presets: Map<string, FacialAnimationPreset> = new Map();
  private isInitialized = false;

  private constructor() {
  }

  /**
   * 获取单例实例
   */
  public static getInstance(): FacialAnimationPresetService {
    if (!FacialAnimationPresetService.instance) {
      FacialAnimationPresetService.instance = new FacialAnimationPresetService();
    }
    return FacialAnimationPresetService.instance;
  }

  /**
   * 初始化服务
   */
  public async initialize(): Promise<void> {
    if (this.isInitialized) {
      return;
    }

    try {
      // 等待引擎初始化
      const engine = EngineService.getEngine();
      if (!engine) {
        throw new Error('引擎未初始化');
      }

      // 加载默认预设
      await this.loadDefaultPresets();
      
      this.isInitialized = true;
      console.log('面部动画预设服务初始化成功');
    } catch (error) {
      console.error('面部动画预设服务初始化失败:', error);
      throw error;
    }
  }

  /**
   * 加载默认预设
   */
  private async loadDefaultPresets(): Promise<void> {
    // 创建默认预设数据
    const defaultPresets: FacialAnimationPreset[] = [
      {
        id: 'happy',
        name: '开心',
        type: FacialAnimationPresetType.STANDARD,
        description: '标准开心表情',
        tags: ['基础', '积极'],
        expression: FacialExpressionType.HAPPY,
        weight: 1.0,
        culture: 'global',
        author: '系统',
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        id: 'sad',
        name: '悲伤',
        type: FacialAnimationPresetType.STANDARD,
        description: '标准悲伤表情',
        tags: ['基础', '消极'],
        expression: FacialExpressionType.SAD,
        weight: 1.0,
        culture: 'global',
        author: '系统',
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        id: 'angry',
        name: '愤怒',
        type: FacialAnimationPresetType.STANDARD,
        description: '标准愤怒表情',
        tags: ['基础', '消极'],
        expression: FacialExpressionType.ANGRY,
        weight: 1.0,
        culture: 'global',
        author: '系统',
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        id: 'surprised',
        name: '惊讶',
        type: FacialAnimationPresetType.STANDARD,
        description: '标准惊讶表情',
        tags: ['基础', '中性'],
        expression: FacialExpressionType.SURPRISED,
        weight: 1.0,
        culture: 'global',
        author: '系统',
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        id: 'chinese_smile',
        name: '含蓄微笑',
        type: FacialAnimationPresetType.CULTURAL,
        description: '中国传统含蓄微笑',
        tags: ['文化', '微笑'],
        culture: 'chinese',
        expressionCombos: [
          { expression: FacialExpressionType.HAPPY, weight: 0.5 },
          { expression: FacialExpressionType.NEUTRAL, weight: 0.5 }
        ],
        author: '系统',
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        id: 'laugh_sequence',
        name: '笑声序列',
        type: FacialAnimationPresetType.ANIMATION_SEQUENCE,
        description: '从微笑到大笑的序列',
        tags: ['序列', '积极'],
        culture: 'global',
        animationSequence: [
          { expression: FacialExpressionType.HAPPY, weight: 0.5, duration: 0.3 },
          { expression: FacialExpressionType.HAPPY, weight: 0.8, duration: 0.2 },
          { expression: FacialExpressionType.HAPPY, weight: 1.0, duration: 0.5 },
          { expression: FacialExpressionType.HAPPY, weight: 0.8, duration: 0.3 },
          { expression: FacialExpressionType.HAPPY, weight: 0.5, duration: 0.2 },
          { expression: FacialExpressionType.NEUTRAL, weight: 0.3, duration: 0.2 }
        ],
        author: '系统',
        createdAt: new Date(),
        updatedAt: new Date()
      }
    ];

    // 将预设添加到本地缓存
    for (const preset of defaultPresets) {
      this.presets.set(preset.id, preset);
    }

    // 尝试从引擎加载预设
    try {
      const engine = EngineService.getEngine();
      if (engine) {
        // 这里可以调用引擎的预设系统来同步预设
        // const presetSystem = engine.getSystem('FacialAnimationPresetSystem');
        // if (presetSystem) {
        //   const enginePresets = presetSystem.getAllPresets();
        //   // 合并引擎预设
        // }
      }
    } catch (error) {
      console.warn('从引擎加载预设失败，使用默认预设:', error);
    }
  }

  /**
   * 获取所有预设
   */
  public async getAllPresets(): Promise<FacialAnimationPreset[]> {
    if (!this.isInitialized) {
      await this.initialize();
    }
    return Array.from(this.presets.values());
  }

  /**
   * 根据ID获取预设
   */
  public async getPreset(id: string): Promise<FacialAnimationPreset | null> {
    if (!this.isInitialized) {
      await this.initialize();
    }
    return this.presets.get(id) || null;
  }

  /**
   * 添加预设
   */
  public async addPreset(preset: FacialAnimationPreset): Promise<boolean> {
    try {
      if (!this.isInitialized) {
        await this.initialize();
      }

      // 检查ID是否已存在
      if (this.presets.has(preset.id)) {
        throw new Error(`预设ID已存在: ${preset.id}`);
      }

      // 设置时间戳
      preset.createdAt = new Date();
      preset.updatedAt = new Date();

      // 添加到本地缓存
      this.presets.set(preset.id, preset);

      // 尝试同步到引擎
      try {
        const engine = EngineService.getEngine();
        if (engine) {
          // 调用引擎的预设系统
          // const presetSystem = engine.getSystem('FacialAnimationPresetSystem');
          // if (presetSystem) {
          //   presetSystem.addPreset(preset);
          // }
        }
      } catch (error) {
        console.warn('同步预设到引擎失败:', error);
      }

      return true;
    } catch (error) {
      console.error('添加预设失败:', error);
      return false;
    }
  }

  /**
   * 更新预设
   */
  public async updatePreset(preset: FacialAnimationPreset): Promise<boolean> {
    try {
      if (!this.isInitialized) {
        await this.initialize();
      }

      // 检查预设是否存在
      if (!this.presets.has(preset.id)) {
        throw new Error(`预设不存在: ${preset.id}`);
      }

      // 更新时间戳
      preset.updatedAt = new Date();

      // 更新本地缓存
      this.presets.set(preset.id, preset);

      // 尝试同步到引擎
      try {
        const engine = EngineService.getEngine();
        if (engine) {
          // 调用引擎的预设系统
          // const presetSystem = engine.getSystem('FacialAnimationPresetSystem');
          // if (presetSystem) {
          //   presetSystem.updatePreset(preset);
          // }
        }
      } catch (error) {
        console.warn('同步预设到引擎失败:', error);
      }

      return true;
    } catch (error) {
      console.error('更新预设失败:', error);
      return false;
    }
  }

  /**
   * 删除预设
   */
  public async deletePreset(id: string): Promise<boolean> {
    try {
      if (!this.isInitialized) {
        await this.initialize();
      }

      // 检查预设是否存在
      if (!this.presets.has(id)) {
        throw new Error(`预设不存在: ${id}`);
      }

      // 从本地缓存删除
      this.presets.delete(id);

      // 尝试从引擎删除
      try {
        const engine = EngineService.getEngine();
        if (engine) {
          // 调用引擎的预设系统
          // const presetSystem = engine.getSystem('FacialAnimationPresetSystem');
          // if (presetSystem) {
          //   presetSystem.removePreset(id);
          // }
        }
      } catch (error) {
        console.warn('从引擎删除预设失败:', error);
      }

      return true;
    } catch (error) {
      console.error('删除预设失败:', error);
      return false;
    }
  }

  /**
   * 应用预设到实体
   */
  public async applyPreset(entityId: string, presetId: string): Promise<boolean> {
    try {
      if (!this.isInitialized) {
        await this.initialize();
      }

      const preset = this.presets.get(presetId);
      if (!preset) {
        throw new Error(`预设不存在: ${presetId}`);
      }

      // 尝试应用到引擎
      try {
        const engine = EngineService.getEngine();
        if (engine) {
          // 调用引擎的预设系统
          // const presetSystem = engine.getSystem('FacialAnimationPresetSystem');
          // const entity = engine.getEntity(entityId);
          // if (presetSystem && entity) {
          //   return presetSystem.applyPreset(entity, presetId);
          // }
        }
      } catch (error) {
        console.warn('应用预设到引擎失败:', error);
      }

      console.log(`应用预设 ${preset.name} 到实体 ${entityId}`);
      return true;
    } catch (error) {
      console.error('应用预设失败:', error);
      return false;
    }
  }

  /**
   * 导入预设
   */
  public async importPresets(presets: FacialAnimationPreset[]): Promise<{ success: number; failed: number }> {
    let success = 0;
    let failed = 0;

    for (const preset of presets) {
      try {
        // 生成新的ID以避免冲突
        const newId = `${preset.id}_${Date.now()}`;
        const newPreset = { ...preset, id: newId };
        
        const result = await this.addPreset(newPreset);
        if (result) {
          success++;
        } else {
          failed++;
        }
      } catch (error) {
        console.error(`导入预设失败: ${preset.name}`, error);
        failed++;
      }
    }

    return { success, failed };
  }

  /**
   * 导出预设
   */
  public async exportPresets(presetIds?: string[]): Promise<FacialAnimationPreset[]> {
    if (!this.isInitialized) {
      await this.initialize();
    }

    if (presetIds) {
      return presetIds
        .map(id => this.presets.get(id))
        .filter(preset => preset !== undefined) as FacialAnimationPreset[];
    } else {
      return Array.from(this.presets.values());
    }
  }
}

// 导出单例实例
export const facialAnimationPresetService = FacialAnimationPresetService.getInstance();
