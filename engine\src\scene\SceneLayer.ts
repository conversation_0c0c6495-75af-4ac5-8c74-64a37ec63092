/**
 * 场景图层
 * 用于管理场景中的实体分组
 */
import type { Entity } from '../core/Entity';
import { Scene } from './Scene';
import { EventEmitter } from '../utils/EventEmitter';
import * as THREE from 'three';

/**
 * 场景图层类型
 */
export enum SceneLayerType {
  /** 普通图层 */
  NORMAL = 'normal',
  /** 图层组 */
  GROUP = 'group'
}

/**
 * 场景图层选项
 */
export interface SceneLayerOptions {
  /** 图层ID */
  id: string;
  /** 图层名称 */
  name: string;
  /** 图层类型 */
  type?: SceneLayerType;
  /** 是否可见 */
  visible?: boolean;
  /** 是否锁定 */
  locked?: boolean;
  /** 是否排除在渲染中 */
  excludeFromRender?: boolean;
  /** 是否排除在物理计算中 */
  excludeFromPhysics?: boolean;
  /** 是否排除在射线检测中 */
  excludeFromRaycast?: boolean;
  /** 图层顺序 */
  order?: number;
  /** 图层标签 */
  tags?: string[];
  /** 图层颜色 */
  color?: THREE.Color;
  /** 父图层ID */
  parentId?: string;
  /** 是否展开（仅用于UI显示） */
  expanded?: boolean;
  /** 自定义数据 */
  userData?: Record<string, any>;
}

/**
 * 场景图层
 */
export class SceneLayer extends EventEmitter {
  /** 图层ID */
  public readonly id: string;

  /** 图层名称 */
  public name: string;

  /** 图层类型 */
  private type: SceneLayerType;

  /** 图层中的实体 */
  private entities: Set<Entity> = new Set();

  /** 是否可见 */
  private visible: boolean;

  /** 是否锁定 */
  private locked: boolean;

  /** 图层顺序 */
  private order: number;

  /** 图层标签 */
  private tags: Set<string> = new Set();

  /** 图层颜色 */
  private color: THREE.Color;

  /** 父图层ID */
  private parentId: string | null = null;

  /** 子图层ID列表 */
  private childrenIds: Set<string> = new Set();

  /** 是否展开（仅用于UI显示） */
  private expanded: boolean;

  /** 自定义数据 */
  private userData: Record<string, any> = {};

  /** 是否已初始化 */
  private initialized: boolean = false;

  /**
   * 创建场景图层
   * @param _scene 所属场景（未使用，避免循环引用）
   * @param options 图层选项
   */
  constructor(_scene: Scene, options: SceneLayerOptions) {
    super();

    // 不存储场景引用，避免循环引用
    this.id = options.id;
    this.name = options.name;

    this.type = options.type !== undefined ? options.type : SceneLayerType.NORMAL;
    this.visible = options.visible !== undefined ? options.visible : true;
    this.locked = options.locked !== undefined ? options.locked : false;
    // 移除未使用的排除选项
    this.order = options.order !== undefined ? options.order : 0;
    this.expanded = options.expanded !== undefined ? options.expanded : true;

    // 设置父图层ID
    if (options.parentId) {
      this.parentId = options.parentId;
    }

    // 添加标签
    if (options.tags) {
      for (const tag of options.tags) {
        this.tags.add(tag);
      }
    }

    this.color = options.color || new THREE.Color(0xffffff);

    // 设置自定义数据
    if (options.userData) {
      this.userData = { ...options.userData };
    }
  }

  /**
   * 初始化图层
   */
  public initialize(): void {
    if (this.initialized) {
      return;
    }

    this.initialized = true;
    this.emit('initialized');
  }

  /**
   * 添加实体
   * @param entity 实体
   * @returns 是否成功添加
   */
  public addEntity(entity: Entity): boolean {
    if (this.entities.has(entity)) {
      return false;
    }

    this.entities.add(entity);

    // 设置实体的可见性
    if (!this.visible) {
      entity.setActive(false);
    }

    // 发出实体添加事件
    this.emit('entityAdded', entity);

    return true;
  }

  /**
   * 移除实体
   * @param entity 实体
   * @returns 是否成功移除
   */
  public removeEntity(entity: Entity): boolean {
    if (!this.entities.has(entity)) {
      return false;
    }

    this.entities.delete(entity);

    // 发出实体移除事件
    this.emit('entityRemoved', entity);

    return true;
  }

  /**
   * 获取图层中的所有实体
   * @returns 实体数组
   */
  public getEntities(): Entity[] {
    return Array.from(this.entities);
  }

  /**
   * 获取图层中的实体数量
   * @returns 实体数量
   */
  public getEntityCount(): number {
    return this.entities.size;
  }

  /**
   * 清空图层
   */
  public clear(): void {
    // 清空前发出事件
    this.emit('beforeClear');

    // 清空实体
    this.entities.clear();

    // 发出清空事件
    this.emit('cleared');
  }

  /**
   * 设置图层可见性
   * @param visible 是否可见
   */
  public setVisible(visible: boolean): void {
    if (this.visible === visible) {
      return;
    }

    this.visible = visible;

    // 更新所有实体的可见性
    for (const entity of Array.from(this.entities)) {
      entity.setActive(visible);
    }

    // 发出可见性变化事件
    this.emit('visibilityChanged', visible);
  }

  /**
   * 是否可见
   * @returns 是否可见
   */
  public isVisible(): boolean {
    return this.visible;
  }

  /**
   * 设置图层锁定状态
   * @param locked 是否锁定
   */
  public setLocked(locked: boolean): void {
    if (this.locked === locked) {
      return;
    }

    this.locked = locked;

    // 发出锁定状态变化事件
    this.emit('lockChanged', locked);
  }

  /**
   * 是否锁定
   * @returns 是否锁定
   */
  public isLocked(): boolean {
    return this.locked;
  }

  /**
   * 设置图层顺序
   * @param order 图层顺序
   */
  public setOrder(order: number): void {
    if (this.order === order) {
      return;
    }

    this.order = order;

    // 发出顺序变化事件
    this.emit('orderChanged', order);
  }

  /**
   * 获取图层顺序
   * @returns 图层顺序
   */
  public getOrder(): number {
    return this.order;
  }

  /**
   * 添加标签
   * @param tag 标签
   * @returns 是否成功添加
   */
  public addTag(tag: string): boolean {
    if (this.tags.has(tag)) {
      return false;
    }

    this.tags.add(tag);

    // 发出标签添加事件
    this.emit('tagAdded', tag);

    return true;
  }

  /**
   * 移除标签
   * @param tag 标签
   * @returns 是否成功移除
   */
  public removeTag(tag: string): boolean {
    if (!this.tags.has(tag)) {
      return false;
    }

    this.tags.delete(tag);

    // 发出标签移除事件
    this.emit('tagRemoved', tag);

    return true;
  }

  /**
   * 是否有标签
   * @param tag 标签
   * @returns 是否有标签
   */
  public hasTag(tag: string): boolean {
    return this.tags.has(tag);
  }

  /**
   * 获取所有标签
   * @returns 标签数组
   */
  public getTags(): string[] {
    return Array.from(this.tags);
  }

  /**
   * 设置图层颜色
   * @param color 颜色
   */
  public setColor(color: THREE.Color): void {
    this.color = color;

    // 发出颜色变化事件
    this.emit('colorChanged', color);
  }

  /**
   * 获取图层颜色
   * @returns 颜色
   */
  public getColor(): THREE.Color {
    return this.color;
  }

  /**
   * 设置自定义数据
   * @param key 键
   * @param value 值
   */
  public setUserData(key: string, value: any): void {
    this.userData[key] = value;

    // 发出自定义数据变化事件
    this.emit('userDataChanged', key, value);
  }

  /**
   * 获取自定义数据
   * @param key 键
   * @returns 值
   */
  public getUserData(key: string): any {
    return this.userData[key];
  }

  /**
   * 获取图层类型
   * @returns 图层类型
   */
  public getType(): SceneLayerType {
    return this.type;
  }

  /**
   * 设置图层类型
   * @param type 图层类型
   */
  public setType(type: SceneLayerType): void {
    if (this.type === type) {
      return;
    }

    this.type = type;

    // 发出类型变化事件
    this.emit('typeChanged', type);
  }

  /**
   * 是否为图层组
   * @returns 是否为图层组
   */
  public isGroup(): boolean {
    return this.type === SceneLayerType.GROUP;
  }

  /**
   * 获取父图层ID
   * @returns 父图层ID
   */
  public getParentId(): string | null {
    return this.parentId;
  }

  /**
   * 设置父图层ID
   * @param parentId 父图层ID
   */
  public setParentId(parentId: string | null): void {
    if (this.parentId === parentId) {
      return;
    }

    const oldParentId = this.parentId;
    this.parentId = parentId;

    // 发出父图层变化事件
    this.emit('parentChanged', parentId, oldParentId);
  }

  /**
   * 添加子图层
   * @param childId 子图层ID
   * @returns 是否成功添加
   */
  public addChild(childId: string): boolean {
    if (this.childrenIds.has(childId)) {
      return false;
    }

    this.childrenIds.add(childId);

    // 发出子图层添加事件
    this.emit('childAdded', childId);

    return true;
  }

  /**
   * 移除子图层
   * @param childId 子图层ID
   * @returns 是否成功移除
   */
  public removeChild(childId: string): boolean {
    if (!this.childrenIds.has(childId)) {
      return false;
    }

    this.childrenIds.delete(childId);

    // 发出子图层移除事件
    this.emit('childRemoved', childId);

    return true;
  }

  /**
   * 获取所有子图层ID
   * @returns 子图层ID数组
   */
  public getChildrenIds(): string[] {
    return Array.from(this.childrenIds);
  }

  /**
   * 是否有子图层
   * @returns 是否有子图层
   */
  public hasChildren(): boolean {
    return this.childrenIds.size > 0;
  }

  /**
   * 获取子图层数量
   * @returns 子图层数量
   */
  public getChildCount(): number {
    return this.childrenIds.size;
  }

  /**
   * 设置展开状态
   * @param expanded 是否展开
   */
  public setExpanded(expanded: boolean): void {
    if (this.expanded === expanded) {
      return;
    }

    this.expanded = expanded;

    // 发出展开状态变化事件
    this.emit('expandedChanged', expanded);
  }

  /**
   * 是否展开
   * @returns 是否展开
   */
  public isExpanded(): boolean {
    return this.expanded;
  }

  /**
   * 销毁图层
   */
  public dispose(): void {
    // 清空图层
    this.clear();

    // 移除所有事件监听器
    this.removeAllListeners();

    this.initialized = false;
  }
}
