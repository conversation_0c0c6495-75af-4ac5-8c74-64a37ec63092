{"name": "dl-engine-editor", "version": "0.1.0", "description": "DL（Digital Learning）引擎编辑器", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:unit": "jest --testPathPattern=src/components", "test:store": "jest --testPathPattern=src/store", "test:integration": "jest --testPathPattern=src/__tests__/integration", "test:ci": "jest --ci --coverage", "lint": "eslint src --ext .ts,.tsx", "lint:fix": "eslint src --ext .ts,.tsx --fix"}, "dependencies": {"@ant-design/charts": "^2.3.0", "@ant-design/icons": "^5.0.1", "@reduxjs/toolkit": "^1.9.5", "@types/dagre": "^0.7.52", "antd": "^5.4.7", "axios": "^1.4.0", "cannon-es": "^0.20.0", "dagre": "^0.8.5", "dl-engine-core": "file:../engine", "echarts": "^5.6.0", "echarts-for-react": "^3.0.2", "html2canvas": "^1.4.1", "i18next": "^22.4.15", "i18next-browser-languagedetector": "^7.0.1", "lodash": "^4.17.21", "rc-dock": "^3.3.1", "react": "^18.2.0", "react-color": "^2.19.3", "react-dom": "^18.2.0", "react-i18next": "^12.2.2", "react-redux": "^8.0.5", "react-router-dom": "^6.11.1", "reactflow": "^11.11.4", "recharts": "^2.15.3", "three": "^0.152.2"}, "devDependencies": {"@testing-library/jest-dom": "^5.16.5", "@testing-library/react": "^14.0.0", "@testing-library/user-event": "^14.4.3", "@types/jest": "^29.5.1", "@types/lodash": "^4.17.17", "@types/node": "^20.1.0", "@types/react": "^18.2.6", "@types/react-color": "^3.0.6", "@types/react-dom": "^18.2.4", "@types/redux-mock-store": "^1.0.3", "@types/three": "^0.152.0", "@typescript-eslint/eslint-plugin": "^5.59.2", "@typescript-eslint/parser": "^5.59.2", "@vitejs/plugin-react": "^4.0.0", "eslint": "^8.40.0", "eslint-plugin-react": "^7.32.2", "eslint-plugin-react-hooks": "^4.6.0", "i18next-http-backend": "^3.0.2", "jest": "^29.5.0", "jest-environment-jsdom": "^29.5.0", "less": "^4.1.3", "react-dnd": "^16.0.1", "react-dnd-html5-backend": "^16.0.1", "redux-mock-store": "^1.5.4", "ts-jest": "^29.1.0", "typescript": "^5.0.4", "vite": "^4.3.5", "vitest": "^0.31.0"}}